/**
 * 增强训练会话管理
 * 集成动作评估引擎，管理训练流程
 */

import { ref, computed, watch } from 'vue'
import { useActionEvaluationEngine } from './useActionEvaluationEngine'
import { useTouchlessActionTimeout } from './useTouchlessActionTimeout'
import { useConnectionStore } from '@/stores/connection'
import { useTrainingStore } from '@/stores/training'
import { useWorkflowStore } from '@/stores/workflow'
import { useTrainingReportStore } from '@/stores/trainingReport'
import { usePatientStore } from '@/stores/patient'
import { useStateTransition } from './useStateTransition'
import { useAudioFeedback } from './useAudioFeedback'
import { saveToLocalFile } from '@/utils/trainingDataManager'

export function useEnhancedTrainingSession() {
  const connectionStore = useConnectionStore()
  const trainingStore = useTrainingStore()
  const workflowStore = useWorkflowStore()
  const trainingReportStore = useTrainingReportStore()
  const patientStore = usePatientStore()
  const stateTransition = useStateTransition()
  const evaluationEngine = useActionEvaluationEngine()
  const audioFeedback = useAudioFeedback()
  const touchlessTimeout = useTouchlessActionTimeout()

  // 训练会话状态
  const isSessionActive = ref(false)
  const sessionStartTime = ref(null)
  const currentActionStartTime = ref(null)

  /**
   * 启动训练会话
   */
  const startSession = () => {
    if (!trainingStore.currentAction) {
      console.warn('[EnhancedTrainingSession] 无当前动作，无法启动会话')
      return false
    }

    console.log('[EnhancedTrainingSession] 启动训练会话')
    isSessionActive.value = true
    sessionStartTime.value = Date.now()

    // 启动训练报告会话
    const patientInfo = {
      patient_id: patientStore.userInfo?.patient_id || 'default_patient',
      patient_name: patientStore.userInfo?.patient_name || '默认患者'
    }
    console.log('[EnhancedTrainingSession] 使用患者信息:', patientInfo)
    trainingReportStore.startSessionReport(patientInfo, `session_${Date.now()}`)

    // 加载当前动作的评估器
    loadCurrentActionDetector()

    return true
  }

  /**
   * 加载当前动作的检测器
   */
  const loadCurrentActionDetector = () => {
    const action = trainingStore.currentAction
    console.log('[EnhancedTrainingSession] 尝试加载动作检测器')
    console.log('[EnhancedTrainingSession] 当前动作:', action)

    if (!action) {
      console.error('[EnhancedTrainingSession] 无当前动作，无法加载检测器')
      return false
    }

    console.log('[EnhancedTrainingSession] 加载动作检测器:', {
      type: action.action_type,
      side: action.side || 'left',
      level: action.difficulty_level || 'medium'
    })

    currentActionStartTime.value = Date.now()

    // 开始记录新的动作
    trainingReportStore.startActionRecord({
      action_id: action.action_id,
      action_type: action.action_type,
      action_name: action.action_name,
      side: action.side || 'left',
      difficulty_level: action.difficulty_level || 'medium'
    })

    const success = evaluationEngine.loadDetector(
      action.action_type,
      action.side || 'left',
      action.difficulty_level || 'medium'
    )

    if (success) {
      console.log('[EnhancedTrainingSession] ✅ 检测器加载成功，评估引擎状态:')
      console.log('  - isActive:', evaluationEngine.isActive.value)
      console.log('  - currentActionType:', evaluationEngine.currentActionType.value)
      console.log('  - currentFeedback:', evaluationEngine.currentFeedback.value)

      // 播报动作指导
      audioFeedback.announceAction(action.action_type, action.side || 'left')

      // 启动超时检测
      startTimeoutDetection(action)
    } else {
      console.error('[EnhancedTrainingSession] ❌ 检测器加载失败')
    }

    return success
  }

  /**
   * 处理动作完成
   */
  const handleActionComplete = () => {
    if (!evaluationEngine.isCompleted.value) return

    const finalScore = evaluationEngine.currentScore.value
    console.log('[EnhancedTrainingSession] 动作完成，最终得分:', finalScore)

    // 完成当前动作记录
    trainingReportStore.completeActionRecord(finalScore, 'completed')

    // 完成当前动作
    trainingStore.completeCurrentAction(finalScore)

    // 停止当前评估器
    evaluationEngine.stopEvaluation()

    // 触发状态转换
    stateTransition.handleActionComplete(finalScore)
  }
  /**
   * 切换到下一个动作
   */
  const switchToNextAction = () => {
    console.log('[EnhancedTrainingSession] 开始切换到下一个动作')
    console.log('[EnhancedTrainingSession] 当前动作索引:', trainingStore.currentActionIndex)
    console.log('[EnhancedTrainingSession] 动作列表长度:', trainingStore.actionList.length)

    // 停止当前评估器
    console.log('[EnhancedTrainingSession] 停止当前评估器')
    evaluationEngine.stopEvaluation()

    // 切换动作
    console.log('[EnhancedTrainingSession] 调用 moveToNextAction')
    const hasNext = trainingStore.moveToNextAction()
    console.log('[EnhancedTrainingSession] moveToNextAction 结果:', hasNext)
    console.log('[EnhancedTrainingSession] 新的当前动作:', trainingStore.currentAction)

    if (hasNext) {
      // 加载新动作的检测器
      console.log('[EnhancedTrainingSession] 有下一个动作，加载新检测器')
      const loadSuccess = loadCurrentActionDetector()
      console.log('[EnhancedTrainingSession] 新检测器加载结果:', loadSuccess)
    } else {
      // 所有动作完成
      console.log('[EnhancedTrainingSession] 所有动作完成，结束会话')
      endSession()
    }

    return hasNext
  }

  /**
   * 结束训练会话
   */
  const endSession = async () => {
    console.log('[EnhancedTrainingSession] 结束训练会话')

    isSessionActive.value = false
    evaluationEngine.stopEvaluation()

    // 完成训练报告会话
    const detailedReport = trainingReportStore.completeSessionReport()
    console.log('[EnhancedTrainingSession] 详细训练报告生成完成:', detailedReport)

    // 生成传统训练报告（保持兼容性）
    const reportData = trainingStore.endTrainingSession()
    console.log('[EnhancedTrainingSession] 传统训练报告生成完成:', reportData)

    // 保存训练数据到后端data目录
    if (detailedReport) {
      try {
        console.log('[EnhancedTrainingSession] 开始保存训练数据到后端data目录')
        const saveResult = await saveToLocalFile(detailedReport)
        if (saveResult.success) {
          console.log('[EnhancedTrainingSession] 训练数据保存成功:', {
            filename: saveResult.filename,
            method: saveResult.method,
            path: saveResult.path
          })
        } else {
          console.warn('[EnhancedTrainingSession] 后端保存失败，已保存到localStorage作为备份:', {
            filename: saveResult.filename,
            method: saveResult.method,
            error: saveResult.error
          })
          // 注意：用户可以在报告页面使用手动导出功能
        }
      } catch (error) {
        console.error('[EnhancedTrainingSession] 保存训练数据时发生错误:', error)
      }
    }

    return {
      detailedReport,
      reportData
    }
  }
  /**
   * 暂停/恢复会话
   */
  const togglePause = () => {
    if (workflowStore.isPaused) {
      console.log('[EnhancedTrainingSession] 恢复训练')
      workflowStore.resumeWorkflow()
    } else {
      console.log('[EnhancedTrainingSession] 暂停训练')
      workflowStore.pauseWorkflow()
    }
  }
  // 监听姿态数据更新
  watch(
    () => connectionStore.poseKeypoints,
    (newKeypoints) => {
      if (!isSessionActive.value || !newKeypoints || workflowStore.isPaused) {
        return
      }
      // 更新评估器
      const result = evaluationEngine.updateEvaluation(newKeypoints)

      // 提供音频反馈
      if (result && result.state) {
        audioFeedback.provideFeedback(result.state, result.score, result.feedback)
      }

      // 更新超时检测进展
      if (result && result.state && result.score !== undefined) {
        updateTimeoutProgress(result.state, result.score)
      }

      // 检查是否完成
      if (evaluationEngine.isCompleted.value) {
        handleActionComplete()
      }
    },
    { deep: true }
  )

  // 监听工作流状态变化
  watch(
    () => workflowStore.currentState,
    (newState, oldState) => {
      console.log('[EnhancedTrainingSession] 工作流状态变化:', oldState, '->', newState)
      console.log('[EnhancedTrainingSession] 当前会话状态:', isSessionActive.value)
      console.log('[EnhancedTrainingSession] 当前动作:', trainingStore.currentAction)

      if (newState === 'training' && !isSessionActive.value) {
        console.log('[EnhancedTrainingSession] 进入训练状态，启动会话')
        const success = startSession()
        console.log('[EnhancedTrainingSession] 会话启动结果:', success)
      } else if (newState !== 'training' && isSessionActive.value) {
        // 如果离开训练状态，暂停会话但不结束
        console.log('[EnhancedTrainingSession] 离开训练状态，暂停会话')
      } else if (newState === 'training' && isSessionActive.value) {
        console.log('[EnhancedTrainingSession] 已在训练状态且会话已激活')
        // 如果已经在训练状态，但可能是动作切换，重新加载检测器
        if (trainingStore.currentAction && oldState === 'preparation') {
          console.log('[EnhancedTrainingSession] 从准备状态进入训练状态，重新加载检测器')
          loadCurrentActionDetector()
        }
      }
    }
  )

  // 监听当前动作变化，在训练状态下自动重新加载评估器
  watch(
    () => trainingStore.currentAction,
    (newAction, oldAction) => {
      console.log('[EnhancedTrainingSession] 当前动作变化:', oldAction?.action_name, '->', newAction?.action_name)

      // 只在训练会话激活且状态为training时重新加载
      if (isSessionActive.value &&
          workflowStore.currentState === 'training' &&
          newAction &&
          newAction !== oldAction) {
        console.log('[EnhancedTrainingSession] 动作切换，重新加载评估器')
        loadCurrentActionDetector()
      }
    },
    { deep: true }
  )

  // 计算属性
  const sessionDuration = computed(() => {
    if (!sessionStartTime.value) return 0
    return Math.floor((Date.now() - sessionStartTime.value) / 1000)
  })

  const currentActionDuration = computed(() => {
    if (!currentActionStartTime.value) return 0
    return Math.floor((Date.now() - currentActionStartTime.value) / 1000)
  })

  // 动作阶段映射（用于UI显示）
  const currentActionStage = computed(() => {
    const state = evaluationEngine.currentState.value
    switch (state) {
      case 'COMPLETED': return 'completed'
      case 'RETURNING': return 'returning'
      case 'HOLDING': return 'holding'
      case 'MOVING_TO_TARGET': return 'moving'
      case 'IDLE': return 'waiting'
      case 'ERROR': return 'error'
      default: return 'waiting'
    }
  })

  /**
   * 启动超时检测
   */
  const startTimeoutDetection = (action) => {
    console.log('[EnhancedTrainingSession] 启动超时检测:', action.action_name)

    touchlessTimeout.startDetection(action, {
      onTimeout: (timeoutData) => {
        console.log('[EnhancedTrainingSession] 动作超时，自动跳过:', timeoutData)
        handleActionTimeout(timeoutData)
      },

      onWarning: (warningData) => {
        console.log('[EnhancedTrainingSession] 超时警告:', warningData)
        // 触发警告UI显示（通过事件或回调）
        if (window.trainingViewInstance) {
          window.trainingViewInstance.showTimeoutWarningDialog(warningData)
        }
      },
      onStagnation: (stagnationData) => {
        console.log('[EnhancedTrainingSession] 动作停滞:', stagnationData)
        // 可以提供额外的指导或提示
      }
    })
  }

  /**
   * 处理动作超时
   */
  const handleActionTimeout = (timeoutData) => {
    console.log('[EnhancedTrainingSession] 处理动作超时')

    // 停止当前评估器
    evaluationEngine.stopEvaluation()

    // 记录超时跳过
    trainingReportStore.completeActionRecord(0, 'timeout_skipped')

    // 显示跳过通知
    if (window.trainingViewInstance) {
      window.trainingViewInstance.showActionSkipDialog({
        action: timeoutData.action,
        reason: 'timeout',
        elapsedTime: timeoutData.elapsedTime
      })
    }

    // 延迟切换到下一个动作（给用户时间看到跳过通知）
    setTimeout(() => {
      switchToNextAction()
    }, 2000)
  }

  /**
   * 更新超时检测进展
   */
  const updateTimeoutProgress = (currentState, currentScore) => {
    if (touchlessTimeout.isDetectionActive.value) {
      touchlessTimeout.updateProgress(currentState, currentScore)
    }
  }

  /**
   * 停止超时检测
   */
  const stopTimeoutDetection = () => {
    touchlessTimeout.stopDetection()
  }

  /**
   * 手动跳过当前动作
   */
  const skipCurrentAction = (reason = 'manual') => {
    if (!trainingStore.currentAction) return false

    console.log('[EnhancedTrainingSession] 手动跳过动作:', reason)

    // 停止检测
    stopTimeoutDetection()
    evaluationEngine.stopEvaluation()

    // 记录跳过
    trainingReportStore.completeActionRecord(0, `manual_skipped_${reason}`)

    // 显示跳过通知
    if (window.trainingViewInstance) {
      window.trainingViewInstance.showActionSkipDialog({
        action: trainingStore.currentAction,
        reason: reason,
        elapsedTime: Date.now() - (currentActionStartTime.value || Date.now())
      })
    }

    // 切换到下一个动作
    setTimeout(() => {
      switchToNextAction()
    }, 1500)

    return true
  }

  /**
   * 重新开始当前动作
   */
  const restartCurrentAction = () => {
    if (!trainingStore.currentAction) return false

    console.log('[EnhancedTrainingSession] 重新开始当前动作')

    // 停止当前检测
    stopTimeoutDetection()
    evaluationEngine.stopEvaluation()

    // 重新加载检测器
    loadCurrentActionDetector()

    return true
  }

  /**
   * 继续下一个动作（用于跳过通知完成后）
   */
  const proceedToNextAction = () => {
    console.log('[EnhancedTrainingSession] 继续下一个动作')
    switchToNextAction()
  }

  // 监听评估引擎分数变化，更新超时检测进展
  watch(
    () => [evaluationEngine.currentScore.value, evaluationEngine.currentState.value],
    ([newScore, newState]) => {
      updateTimeoutProgress(newState, newScore)
    }
  )

  return {
    // 状态
    isSessionActive,
    sessionDuration,
    currentActionDuration,
    
    // 评估引擎状态
    currentScore: evaluationEngine.currentScore,
    currentFeedback: evaluationEngine.currentFeedback,
    currentState: evaluationEngine.currentState,
    currentActionStage,
    isActionCompleted: evaluationEngine.isCompleted,
    
    // 方法
    startSession,
    endSession,
    togglePause,
    switchToNextAction,
    loadCurrentActionDetector,

    // 无接触异常处理方法
    skipCurrentAction,
    restartCurrentAction,
    proceedToNextAction,
    startTimeoutDetection,
    stopTimeoutDetection,

    // 评估引擎方法
    resetCurrentDetector: evaluationEngine.resetDetector,

    // 音频反馈控制
    audioEnabled: audioFeedback.isEnabled,
    audioVolume: audioFeedback.volume,
    toggleAudio: audioFeedback.toggleAudio,
    setAudioVolume: audioFeedback.setVolume
  }
}
