/**
 * 优化的身份验证系统
 * 简化状态管理，统一处理逻辑，支持动态配置
 */
import { ref, computed, watch } from 'vue'
import { useConnectionStore } from '@/stores/connection'
import { usePatientStore } from '@/stores/patient'
import { useWorkflowStore } from '@/stores/workflow'
import { useTrainingStore } from '@/stores/training'
import { useTrainingReportStore } from '@/stores/trainingReport'

export function useSimpleIdentityVerification() {
  const connectionStore = useConnectionStore()
  const patientStore = usePatientStore()
  const workflowStore = useWorkflowStore()
  const trainingStore = useTrainingStore()
  const trainingReportStore = useTrainingReportStore()

  // 配置常量
  const DETECTION_TIMEOUT = 3000
  const TRAINING_RESET_TIMEOUT = 10000
  const REPORT_RESET_TIMEOUT = 30000
  const OTHER_RESET_TIMEOUT = 60000

  // 核心状态
  const isVerificationActive = ref(false)
  const currentIssueType = ref(null) // 'missing' | 'unauthorized' | null
  const issueStartTime = ref(null)
  const lastValidDetectionTime = ref(null)

  // 定时器
  const detectionTimer = ref(null)
  const resetTimer = ref(null)
  const debounceTimer = ref(null)

  // 回调函数
  const callbacks = ref({})

  // 计算属性
  const expectedPatientId = computed(() => patientStore.userInfo?.patient_id)
  const currentPatientId = computed(() => connectionStore.patientId)

  // 获取当前阶段的超时配置
  const currentTimeoutConfig = computed(() => {
    switch (workflowStore.currentState) {
      case 'training': return TRAINING_RESET_TIMEOUT
      case 'reporting': return REPORT_RESET_TIMEOUT
      default: return OTHER_RESET_TIMEOUT
    }
  })

  // 清理所有定时器
  const clearAllTimers = () => {
    [detectionTimer, resetTimer, debounceTimer].forEach(timer => {
      if (timer.value) {
        clearTimeout(timer.value)
        timer.value = null
      }
    })
  }

  // 启动身份验证
  const startVerification = (options = {}) => {
    console.log('[SimpleIdentity] 启动身份验证，预期用户ID:', expectedPatientId.value)

    if (!expectedPatientId.value) {
      console.warn('[SimpleIdentity] 无预期用户ID，无法启动验证')
      return false
    }

    // 重置状态
    isVerificationActive.value = true
    currentIssueType.value = null
    issueStartTime.value = null
    lastValidDetectionTime.value = Date.now()
    callbacks.value = options

    // 开始监听
    startPatientIdWatcher()
    return true
  }

  // 停止身份验证
  const stopVerification = () => {
    console.log('[SimpleIdentity] 停止身份验证')
    isVerificationActive.value = false
    currentIssueType.value = null
    issueStartTime.value = null
    clearAllTimers()
  }

  // 开始监听patientId变化
  const startPatientIdWatcher = () => {
    // 监听patientId变化
    watch(() => connectionStore.patientId, (newPatientId) => {
      if (!isVerificationActive.value) return
      handlePatientIdChange(newPatientId)
    })

    // 延迟5秒后开始首次检测
    setTimeout(() => {
      if (isVerificationActive.value) {
        console.log('[SimpleIdentity] 开始首次身份检测')
        handlePatientIdChange(connectionStore.patientId)
      }
    }, 5000)
  }

  // 处理patientId变化
  const handlePatientIdChange = (detectedPatientId) => {
    const expected = expectedPatientId.value

    console.log('[SimpleIdentity] 检测到patientId变化:', {
      detected: detectedPatientId,
      expected: expected,
      currentIssue: currentIssueType.value
    })

    // 正确用户 - 立即处理
    if (detectedPatientId === expected) {
      console.log('[SimpleIdentity] ✅ 用户ID匹配')
      clearTimeout(debounceTimer.value)
      handleValidUser()
      return
    }

    // 其他情况 - 防抖处理
    clearTimeout(debounceTimer.value)
    debounceTimer.value = setTimeout(() => {
      processPatientIdChange(detectedPatientId, expected)
    }, 500)
  }

  // 处理ID变化的逻辑
  const processPatientIdChange = (detectedPatientId, expected) => {
    console.log('[SimpleIdentity] 防抖后处理patientId:', detectedPatientId)

    if (!detectedPatientId) {
      handleIssue('missing')
    } else if (detectedPatientId !== expected) {
      handleIssue('unauthorized', detectedPatientId)
    }
  }

  // 统一的问题处理
  const handleIssue = (issueType, detectedId = null) => {
    // 如果已经在处理相同问题，检查是否超过3秒
    if (currentIssueType.value === issueType) {
      const issueTime = Date.now() - issueStartTime.value
      if (issueTime >= DETECTION_TIMEOUT) {
        triggerIssueAction(issueType, detectedId)
      }
      return
    }

    // 开始新的问题检测
    console.log(`[SimpleIdentity] 开始检测${issueType}问题`)
    currentIssueType.value = issueType
    issueStartTime.value = Date.now()

    // 3秒后检查是否仍然存在问题
    detectionTimer.value = setTimeout(() => {
      if (currentIssueType.value === issueType &&
          Date.now() - issueStartTime.value >= DETECTION_TIMEOUT) {
        triggerIssueAction(issueType, detectedId)
      }
    }, DETECTION_TIMEOUT)
  }

  // 处理正确用户
  const handleValidUser = () => {
    lastValidDetectionTime.value = Date.now()

    if (currentIssueType.value) {
      console.log('[SimpleIdentity] 用户已返回，恢复训练')
      const wasIssue = currentIssueType.value

      // 重置状态
      currentIssueType.value = null
      issueStartTime.value = null
      clearTimeout(resetTimer.value)

      // 触发回调
      callbacks.value.onUserReturned?.({
        type: 'user_returned',
        previousIssue: wasIssue,
        timestamp: Date.now()
      })
    }
  }

  // 统一的触发动作
  const triggerIssueAction = (issueType, detectedId = null) => {
    console.log(`[SimpleIdentity] 确认${issueType}问题，暂停训练`)

    // 暂停训练
    if (!workflowStore.isPaused) {
      workflowStore.pauseWorkflow()
    }

    // 启动重置定时器
    startResetTimer()

    // 触发对应回调
    const callbackData = {
      type: issueType === 'missing' ? 'user_missing' : 'unauthorized_user',
      timestamp: Date.now()
    }

    if (issueType === 'unauthorized') {
      callbackData.detectedId = detectedId
      callbackData.expectedId = expectedPatientId.value
      callbacks.value.onUnauthorizedUser?.(callbackData)
    } else {
      callbacks.value.onUserMissing?.(callbackData)
    }
  }

  // 启动重置定时器
  const startResetTimer = () => {
    clearTimeout(resetTimer.value)
    resetTimer.value = setTimeout(() => {
      handleTimeoutReset()
    }, currentTimeoutConfig.value)
  }

  // 处理超时重置
  const handleTimeoutReset = () => {
    const currentState = workflowStore.currentState

    try {
      if (currentState === 'training') {
        // 训练阶段：保存记录并跳转报告页
        console.log('[SimpleIdentity] 训练阶段超时，保存记录并跳转报告页')

        // 保存当前动作记录
        if (trainingStore.currentAction) {
          trainingReportStore.startActionRecord(trainingStore.currentAction)
          trainingReportStore.completeActionRecord(0, 'user_left_timeout')
        }

        // 标记剩余动作为未完成
        const remainingActions = trainingStore.actionList.filter(
          action => action.action_id !== trainingStore.currentAction?.action_id
        )

        remainingActions.forEach(action => {
          trainingReportStore.startActionRecord(action)
          trainingReportStore.completeActionRecord(0, 'user_left_incomplete')
        })

        // 结束训练会话并跳转报告页
        const report = trainingReportStore.endSessionReport()
        workflowStore.transitionTo('reporting')

        callbacks.value.onResetToLogin?.({
          type: 'training_timeout_to_report',
          reason: 'user_left_too_long',
          timestamp: Date.now(),
          report: report
        })
      } else {
        // 其他阶段：直接重置到登录页
        resetToLogin('timeout')
      }
    } catch (error) {
      console.error('[SimpleIdentity] 处理超时重置时出错:', error)
      resetToLogin('timeout_error')
    }
  }

  // 重置到登录页
  const resetToLogin = (reason = 'timeout') => {
    console.log(`[SimpleIdentity] 重置到登录页，原因: ${reason}`)

    stopVerification()
    patientStore.resetPatientData()
    workflowStore.transitionTo('waiting')

    callbacks.value.onResetToLogin?.({
      type: 'reset_to_login',
      reason: reason,
      timestamp: Date.now()
    })
  }

  // 返回接口
  return {
    // 状态
    isVerificationActive,
    currentIssueType,
    expectedPatientId,
    currentPatientId,
    currentTimeoutConfig,

    // 方法
    startVerification,
    stopVerification,
    resetToLogin,

    // 配置
    DETECTION_TIMEOUT,
    TRAINING_RESET_TIMEOUT,
    REPORT_RESET_TIMEOUT,
    OTHER_RESET_TIMEOUT
  }
}