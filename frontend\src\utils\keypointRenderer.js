// utils/keypointRenderer.js
/**
 * 关键点渲染器类
 * 负责在画布上绘制姿态关键点和连接线
 */
export class KeypointRenderer {
  constructor(canvas, options = {}) {
    this.canvas = canvas;
    this.ctx = canvas.getContext('2d');
    this.keypoints = [];
    this.imageElement = null;
    this.transformParams = null; // 存储转换参数
    this.transformedKeypoints = []; // 存储转换后的关键点

    this.options = {
      pointRadius: 4,
      lineWidth: 2,
      pointColor: 'red',
      lineColor: 'lime',
      confidenceThreshold: 0.4,
      smoothingFactor: 0.4,
      // 新增：关键点消失相关配置
      fadeOutFactor: 0.15, // 置信度衰减因子
      maxMissingFrames: 10, // 最大缺失帧数
      connections: [],
      // 新增：颜色连接支持
      connectionsWithColors: [], // 包含颜色信息的连接数组
      useColoredConnections: false, // 是否使用彩色连接
      ...options,
    };

    this.isRendering = false;
    this.animationId = null;
    this.smoothedKeypoints = [];
    this.keypointTimestamps = []; // 关键点时间戳
    this.missingFrameCounts = []; // 缺失帧计数
  }

  /**
   * 更新关键点数据
   * @param {Array} keypoints - 归一化关键点数组
   */
  updateKeypoints(keypoints) {
    this.keypoints = keypoints || [];
    this.applySmoothing();
    this.calculateTransformedKeypoints(); // 计算转换后的坐标
  }

  /**
   * 更新图像元素
   * @param {HTMLImageElement} imageElement - 图像元素
   */
  updateImageElement(imageElement) {
    this.imageElement = imageElement;
    this.calculateTransformedKeypoints(); // 重新计算转换坐标
  }

  /**
   * 更新画布
   * @param {HTMLCanvasElement} canvas - 新的画布元素
   */
  updateCanvas(canvas) {
    this.canvas = canvas;
    this.ctx = canvas.getContext('2d');
    this.calculateTransformedKeypoints(); // 重新计算转换坐标
  }

  /**
   * 计算转换参数和转换后的关键点
   */
  calculateTransformedKeypoints() {
    if (!this.canvas || !this.imageElement || !this.keypoints.length) {
      this.transformParams = null;
      this.transformedKeypoints = [];
      return;
    }

    const canvasWidth = this.canvas.clientWidth;
    const canvasHeight = this.canvas.clientHeight;
    const imageNaturalWidth = this.imageElement.naturalWidth;
    const imageNaturalHeight = this.imageElement.naturalHeight;

    // 计算转换参数
    if (imageNaturalWidth === 0 || imageNaturalHeight === 0) {
      this.transformParams = null;
      this.transformedKeypoints = [];
      return;
    }

    const imageAspectRatio = imageNaturalWidth / imageNaturalHeight;
    const canvasAspectRatio = canvasWidth / canvasHeight;

    let renderWidth, renderHeight, xOffset, yOffset;

    if (imageAspectRatio > canvasAspectRatio) {
      renderHeight = canvasHeight;
      renderWidth = canvasHeight * imageAspectRatio;
      xOffset = (canvasWidth - renderWidth) / 2;
      yOffset = 0;
    } else {
      renderWidth = canvasWidth;
      renderHeight = canvasWidth / imageAspectRatio;
      xOffset = 0;
      yOffset = (canvasHeight - renderHeight) / 2;
    }

    this.transformParams = {
      renderWidth,
      renderHeight,
      xOffset,
      yOffset,
      canvasWidth,
      canvasHeight
    };

    // 转换关键点坐标
    this.transformedKeypoints = this.smoothedKeypoints.map((point) => {
      if (!point || point.length < 3 || point[2] < this.options.confidenceThreshold) {
        return null;
      }
      return {
        x: point[0] * renderWidth + xOffset,
        y: point[1] * renderHeight + yOffset,
        confidence: point[2]
      };
    });
  }

  /**
   * 获取转换后的关键点数据
   * @returns {Array} - 转换后的关键点数组
   */
  getTransformedKeypoints() {
    return this.transformedKeypoints;
  }

  /**
   * 获取转换参数
   * @returns {Object|null} - 转换参数对象
   */
  getTransformParams() {
    return this.transformParams;
  }

  /**
   * 计算置信度衰减
   * @param {number} originalConfidence - 原始置信度
   * @param {number} missingFrames - 缺失帧数
   * @returns {number} - 衰减后的置信度
   */
  calculateConfidenceDecay(originalConfidence, missingFrames) {
    const fadeOutFactor = this.options.fadeOutFactor;
    const maxMissingFrames = this.options.maxMissingFrames;
    // 使用指数衰减函数
    const decayRate = Math.pow(1 - fadeOutFactor, missingFrames);
    const decayedConfidence = originalConfidence * decayRate;
    // 额外的时间衰减：随着缺失帧数增加，衰减速度加快
    const timeDecayFactor = 1 - (missingFrames / maxMissingFrames) * 0.3;

    return decayedConfidence * Math.max(0.1, timeDecayFactor);
  }

  /**
   * 应用平滑处理（带渐进消失策略）
   */
  applySmoothing() {
    if (!this.keypoints || this.keypoints.length === 0) {
      this.smoothedKeypoints = [];
      this.keypointTimestamps = [];
      this.missingFrameCounts = [];
      return;
    }

    // 初始化数组
    if (this.smoothedKeypoints.length === 0) {
      this.smoothedKeypoints = [...this.keypoints];
      this.keypointTimestamps = new Array(this.keypoints.length).fill(Date.now());
      this.missingFrameCounts = new Array(this.keypoints.length).fill(0);
      return;
    }

    // 确保数组长度一致
    while (this.smoothedKeypoints.length < this.keypoints.length) {
      this.smoothedKeypoints.push(null);
      this.keypointTimestamps.push(Date.now());
      this.missingFrameCounts.push(0);
    }

    const currentTime = Date.now();
    const factor = this.options.smoothingFactor;
    const maxMissingFrames = this.options.maxMissingFrames;

    this.smoothedKeypoints = this.keypoints.map((point, index) => {
      const prevPoint = this.smoothedKeypoints[index];
      const isCurrentPointValid = point && point.length >= 3 && point[2] >= this.options.confidenceThreshold;

      if (isCurrentPointValid) {
        // 当前点有效，重置缺失计数并更新时间戳
        this.missingFrameCounts[index] = 0;
        this.keypointTimestamps[index] = currentTime;

        if (!prevPoint || prevPoint.length < 3) {
          return point;
        }
        // 正常的平滑处理
        return [
          prevPoint[0] * factor + point[0] * (1 - factor),
          prevPoint[1] * factor + point[1] * (1 - factor),
          point[2]
        ];
      } else {
        // 当前点无效，处理渐进消失
        if (!prevPoint || prevPoint.length < 3) {
          return null; // 没有历史点，直接返回null
        }

        // 增加缺失帧计数
        this.missingFrameCounts[index]++;

        // 如果超过最大缺失帧数，直接清除
        if (this.missingFrameCounts[index] > maxMissingFrames) {
          return null;
        }

        // 使用优化的置信度衰减算法
        const fadedConfidence = this.calculateConfidenceDecay(
          prevPoint[2],
          this.missingFrameCounts[index]
        );

        // 如果置信度降到阈值以下，清除关键点
        if (fadedConfidence < this.options.confidenceThreshold) {
          return null;
        }

        // 返回置信度衰减后的历史点
        return [
          prevPoint[0],
          prevPoint[1],
          fadedConfidence
        ];
      }
    });
  }

  /**
   * 绘制关键点和连接线
   */
  draw() {
    if (!this.ctx || !this.transformedKeypoints.length) return;

    // 获取实际的Canvas尺寸（考虑设备像素比）
    const canvasWidth = this.canvas.width;
    const canvasHeight = this.canvas.height;

    // 完全清除画布
    this.ctx.save();
    this.ctx.setTransform(1, 0, 0, 1, 0, 0);
    this.ctx.clearRect(0, 0, canvasWidth, canvasHeight);
    this.ctx.restore();
    // 绘制关键点
    this.drawKeypoints();
    // 绘制连接线
    this.drawConnections();
  }

  /**
   * 绘制连接线
   */
  drawConnections() {
    this.ctx.lineWidth = this.options.lineWidth;
    this.ctx.lineCap = 'round';
    this.ctx.lineJoin = 'round';

    if (this.options.useColoredConnections && this.options.connectionsWithColors.length > 0) {
      // 使用彩色连接模式
      this.drawColoredConnections();
    } else {
      // 使用传统单色连接模式
      this.drawSingleColorConnections();
    }
  }

  /**
   * 绘制单色连接线（向后兼容）
   */
  drawSingleColorConnections() {
    this.ctx.strokeStyle = this.options.lineColor;
    this.ctx.beginPath();

    for (const [startIdx, endIdx] of this.options.connections) {
      const startPoint = this.transformedKeypoints[startIdx];
      const endPoint = this.transformedKeypoints[endIdx];

      if (startPoint && endPoint &&
          startPoint.confidence > this.options.confidenceThreshold &&
          endPoint.confidence > this.options.confidenceThreshold) {
        this.ctx.moveTo(startPoint.x, startPoint.y);
        this.ctx.lineTo(endPoint.x, endPoint.y);
      }
    }

    this.ctx.stroke();
  }

  /**
   * 绘制彩色连接线
   */
  drawColoredConnections() {
    for (const connectionInfo of this.options.connectionsWithColors) {
      const [startIdx, endIdx] = connectionInfo.connection;
      const startPoint = this.transformedKeypoints[startIdx];
      const endPoint = this.transformedKeypoints[endIdx];

      if (startPoint && endPoint &&
          startPoint.confidence > this.options.confidenceThreshold &&
          endPoint.confidence > this.options.confidenceThreshold) {

        // 设置连接线颜色
        const [r, g, b] = connectionInfo.color;
        this.ctx.strokeStyle = `rgb(${r}, ${g}, ${b})`;

        // 绘制单条连接线
        this.ctx.beginPath();
        this.ctx.moveTo(startPoint.x, startPoint.y);
        this.ctx.lineTo(endPoint.x, endPoint.y);
        this.ctx.stroke();
      }
    }
  }

  /**
   * 绘制关键点
   */
  drawKeypoints() {
    this.ctx.fillStyle = this.options.pointColor;

    for (const point of this.transformedKeypoints) {
      if (point && point.confidence > this.options.confidenceThreshold) {
        this.ctx.beginPath();
        this.ctx.arc(point.x, point.y, this.options.pointRadius, 0, 2 * Math.PI);
        this.ctx.fill();
      }
    }
  }

  /**
   * 开始渲染循环
   */
  startRenderLoop() {
    if (this.isRendering) return;
    
    this.isRendering = true;
    const renderFrame = () => {
      if (!this.isRendering) return;
      this.draw();
      this.animationId = requestAnimationFrame(renderFrame);
    };
    
    renderFrame();
  }

  /**
   * 停止渲染循环
   */
  stopRenderLoop() {
    this.isRendering = false;
    if (this.animationId) {
      cancelAnimationFrame(this.animationId);
      this.animationId = null;
    }
  }

  /**
   * 销毁渲染器
   */
  destroy() {
    this.stopRenderLoop();
    this.keypoints = [];
    this.transformedKeypoints = [];
    this.smoothedKeypoints = [];
    this.keypointTimestamps = [];
    this.missingFrameCounts = [];
    this.transformParams = null;
    this.imageElement = null;
  }
}


