<template>
  <div class="min-h-screen bg-gradient-to-br from-blue-500 via-purple-500 to-purple-700 p-4 md:p-6 overflow-y-auto">
    <!-- 报告头部 -->
    <el-card class="mb-6" shadow="always">
      <div class="flex flex-col md:flex-row justify-between items-center">
        <h1 class="text-3xl md:text-4xl font-bold text-gray-800 mb-4 md:mb-0">
          <el-icon class="mr-3"><Document /></el-icon>
          康复训练报告
        </h1>
        <div class="flex gap-3">
          <el-button
            type="primary"
            @click="exportReport"
            :icon="Download"
            size="large"
          >
            导出报告
          </el-button>
        </div>
      </div>
    </el-card>
    <!-- 加载状态 -->
    <el-card v-if="loading" class="text-center" shadow="always">
      <div class="py-16">
        <el-icon class="text-6xl text-blue-500 mb-4"><Loading /></el-icon>
        <p class="text-gray-600 text-lg">正在生成报告...</p>
      </div>
    </el-card>

    <!-- 报告内容 -->
    <div v-else-if="reportData" class="space-y-6">
      <!-- 基本信息卡片 -->
      <el-card shadow="always">
        <template #header>
          <div class="flex items-center">
            <el-icon class="mr-2 text-blue-500"><User /></el-icon>
            <span class="text-xl font-bold">基本信息</span>
          </div>
        </template>

        <el-row :gutter="16">
          <el-col :xs="24" :sm="12" :md="8" v-for="(item, index) in basicInfoItems" :key="index" class="mb-4">
            <el-card class="info-item-card" shadow="hover">
              <div class="flex justify-between items-center">
                <span class="text-gray-600 font-medium">{{ item.label }}</span>
                <el-tag :type="item.type || 'info'" size="large">{{ item.value }}</el-tag>
              </div>
            </el-card>
          </el-col>
        </el-row>
      </el-card>

      <!-- 总体评价卡片 -->
      <el-card shadow="always">
        <template #header>
          <div class="flex items-center">
            <el-icon class="mr-2 text-green-500"><Trophy /></el-icon>
            <span class="text-xl font-bold">总体评价</span>
          </div>
        </template>

        <el-row :gutter="24">
          <el-col :xs="24" :lg="8" class="text-center mb-6 lg:mb-0">
            <div class="score-circle-container">
              <el-progress
                type="circle"
                :percentage="reportData.overall_assessment.overall_score"
                :width="150"
                :stroke-width="8"
                :color="getProgressColor(reportData.overall_assessment.overall_score)"
              >
                <template #default="{ percentage }">
                  <span class="text-3xl font-bold">{{ percentage }}</span>
                  <div class="text-sm text-gray-500">总分</div>
                </template>
              </el-progress>
            </div>
          </el-col>

          <el-col :xs="24" :lg="16">
            <div class="mb-4">
              <el-tag
                :type="getLevelTagType(reportData.overall_assessment.level)"
                size="large"
                effect="dark"
              >
                {{ getLevelLabel(reportData.overall_assessment.level) }}
              </el-tag>
            </div>

            <el-alert
              :title="reportData.overall_assessment.summary"
              type="info"
              :closable="false"
              show-icon
              class="mb-6"
            />

            <el-collapse v-model="activeCollapse">
              <el-collapse-item
                v-if="reportData.overall_assessment.achievements.length"
                title="主要成就"
                name="achievements"
              >
                <template #title>
                  <el-icon class="mr-2 text-green-500"><Check /></el-icon>
                  主要成就
                </template>
                <ul class="space-y-2">
                  <li v-for="achievement in reportData.overall_assessment.achievements" :key="achievement" class="flex items-start gap-2">
                    <el-icon class="text-green-500 mt-1"><Check /></el-icon>
                    <span class="text-gray-600">{{ achievement }}</span>
                  </li>
                </ul>
              </el-collapse-item>

              <el-collapse-item
                v-if="reportData.overall_assessment.focus_areas.length"
                title="需要关注的领域"
                name="focus"
              >
                <template #title>
                  <el-icon class="mr-2 text-orange-500"><Warning /></el-icon>
                  需要关注的领域
                </template>
                <ul class="space-y-2">
                  <li v-for="area in reportData.overall_assessment.focus_areas" :key="area" class="flex items-start gap-2">
                    <el-icon class="text-orange-500 mt-1"><Warning /></el-icon>
                    <span class="text-gray-600">{{ area }}</span>
                  </li>
                </ul>
              </el-collapse-item>
              <el-collapse-item
                v-if="reportData.overall_assessment.next_goals.length"
                title="下次训练目标"
                name="goals"
              >
                <template #title>
                  <el-icon class="mr-2 text-blue-500"><Right /></el-icon>
                  下次训练目标
                </template>
                <ul class="space-y-2">
                  <li v-for="goal in reportData.overall_assessment.next_goals" :key="goal" class="flex items-start gap-2">
                    <el-icon class="text-blue-500 mt-1"><Right /></el-icon>
                    <span class="text-gray-600">{{ goal }}</span>
                  </li>
                </ul>
              </el-collapse-item>
            </el-collapse>
          </el-col>
        </el-row>
      </el-card>

      <!-- 训练统计卡片 -->
      <el-card shadow="always">
        <template #header>
          <div class="flex items-center">
            <el-icon class="mr-2 text-purple-500"><DataAnalysis /></el-icon>
            <span class="text-xl font-bold">训练统计</span>
          </div>
        </template>

        <el-row :gutter="16" >
          <el-col :xs="12" :md="6" v-for="(stat, index) in statisticsItems" :key="index" class="mb-4">
            <el-card class="stat-card text-center" shadow="hover" :body-style="{ padding: '20px' }">
              <div class="text-3xl font-bold mb-2" :class="stat.color">{{ stat.value }}</div>
              <div class="text-sm text-gray-500">{{ stat.label }}</div>
            </el-card>
          </el-col>
        </el-row>
      </el-card>

      <!-- 详细动作记录 -->
      <div class="bg-white/95 backdrop-blur-sm rounded-2xl p-6 shadow-xl">
        <h2 class="text-2xl font-bold text-gray-800 mb-6 pb-3 border-b-2 border-gray-200">动作详情</h2>
        <div class="space-y-4">
          <div
            v-for="(action, index) in reportData.detailed_actions"
            :key="action.action_id"
            class="border border-gray-200 rounded-xl overflow-hidden transition-all duration-300 hover:shadow-lg"
          >
            <div class="flex items-center p-5 cursor-pointer bg-gray-50 hover:bg-gray-100 transition-colors duration-300" @click="toggleActionExpand(index)">
              <div class="flex-1">
                <h3 class="text-xl font-semibold text-gray-800 mb-2">{{ action.action_name }}</h3>
                <div class="flex flex-wrap gap-3 text-sm text-gray-600">
                  <span class="px-3 py-1 bg-gray-200 rounded-lg font-medium">{{ action.side === 'left' ? '左侧' : '右侧' }}</span>
                  <span class="px-3 py-1 bg-gray-200 rounded-lg font-medium">{{ getDifficultyLabel(action.difficulty_level) }}</span>
                  <span class="px-3 py-1 bg-gray-200 rounded-lg font-medium">{{ formatDuration(action.duration_seconds) }}</span>
                </div>
              </div>
              <div class="text-2xl font-bold px-4 py-2 rounded-lg mr-4" :class="getScoreClass(action.final_score)">
                {{ action.final_score }}
              </div>
              <div class="text-xl text-gray-400 transition-transform duration-300" :class="{ 'rotate-180': expandedActions.includes(index) }">
                <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"></path>
                </svg>
              </div>
            </div>

            <div v-show="expandedActions.includes(index)" class="p-6 bg-white border-t border-gray-200">
              <!-- 阶段评分 -->
              <div class="mb-8">
                <h4 class="text-lg font-semibold text-gray-800 mb-4 pb-2 border-b border-gray-200">阶段评分</h4>
                <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                  <div
                    v-for="stage in action.stage_scores"
                    :key="stage.stage"
                    class="p-4 bg-gray-50 rounded-xl border-l-4 border-blue-500"
                  >
                    <div class="font-semibold text-gray-800 mb-2">{{ getStageLabel(stage.stage) }}</div>
                    <div class="flex items-baseline gap-1 mb-1">
                      <span class="text-xl font-bold text-blue-600">{{ stage.score }}</span>
                      <span class="text-gray-500 text-sm">/{{ stage.maxScore }}</span>
                    </div>
                    <div class="text-sm font-semibold text-green-600 mb-2">{{ stage.percentage }}%</div>
                    <div class="text-xs text-gray-600 italic">{{ stage.feedback }}</div>
                  </div>
                </div>
              </div>

              <!-- 动作建议 -->
              <div v-if="action.advice" class="mb-8">
                <h4 class="text-lg font-semibold text-gray-800 mb-4 pb-2 border-b border-gray-200">动作建议</h4>
                <div class="bg-gray-50 p-5 rounded-xl">
                  <p class="text-lg font-semibold text-gray-800 mb-4">{{ action.advice.overall }}</p>

                  <div v-if="action.advice.strengths.length" class="mb-4">
                    <h5 class="text-base font-semibold text-gray-700 mb-2">优点</h5>
                    <ul class="space-y-1">
                      <li v-for="strength in action.advice.strengths" :key="strength" class="flex items-start gap-2">
                        <span class="text-blue-500 font-bold mt-1">•</span>
                        <span class="text-gray-600 text-sm">{{ strength }}</span>
                      </li>
                    </ul>
                  </div>

                  <div v-if="action.advice.improvements.length" class="mb-4">
                    <h5 class="text-base font-semibold text-gray-700 mb-2">改进建议</h5>
                    <ul class="space-y-1">
                      <li v-for="improvement in action.advice.improvements" :key="improvement" class="flex items-start gap-2">
                        <span class="text-blue-500 font-bold mt-1">•</span>
                        <span class="text-gray-600 text-sm">{{ improvement }}</span>
                      </li>
                    </ul>
                  </div>

                  <div v-if="action.advice.tips.length">
                    <h5 class="text-base font-semibold text-gray-700 mb-2">练习技巧</h5>
                    <ul class="space-y-1">
                      <li v-for="tip in action.advice.tips" :key="tip" class="flex items-start gap-2">
                        <span class="text-blue-500 font-bold mt-1">•</span>
                        <span class="text-gray-600 text-sm">{{ tip }}</span>
                      </li>
                    </ul>
                  </div>
                </div>
              </div>
              <!-- 性能指标 -->
              <div>
                <h4 class="text-lg font-semibold text-gray-800 mb-4 pb-2 border-b border-gray-200">性能指标</h4>
                <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
                  <div class="flex justify-between items-center p-4 bg-gray-50 rounded-xl">
                    <span class="font-semibold text-gray-600">最高得分：</span>
                    <span class="text-gray-800 font-medium">{{ action.performance_metrics.max_score_reached }}</span>
                  </div>
                  <div class="flex justify-between items-center p-4 bg-gray-50 rounded-xl">
                    <span class="font-semibold text-gray-600">最低得分：</span>
                    <span class="text-gray-800 font-medium">{{ action.performance_metrics.min_score_reached }}</span>
                  </div>
                  <div class="flex justify-between items-center p-4 bg-gray-50 rounded-xl">
                    <span class="font-semibold text-gray-600">稳定性指数：</span>
                    <span class="text-gray-800 font-medium">{{ action.performance_metrics.stability_index }}</span>
                  </div>
                  <div class="flex justify-between items-center p-4 bg-gray-50 rounded-xl">
                    <span class="font-semibold text-gray-600">完成效率：</span>
                    <span class="text-gray-800 font-medium">{{ action.performance_metrics.completion_efficiency }}</span>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

   
  </div>
</template>

<script setup>
import { ref, onMounted, onUnmounted, computed } from 'vue'
import { useRouter } from 'vue-router'
import { useTrainingReportStore } from '@/stores/trainingReport'
import { usePatientStore } from '@/stores/patient'
import { useTrainingStore } from '@/stores/training'
import { useWorkflowStore } from '@/stores/workflow'
import { useConnectionStore } from '@/stores/connection'
import { useSimpleIdentityVerification } from '@/composables/useSimpleIdentityVerification'
import { OVERALL_ASSESSMENT_LEVELS } from '@/types/trainingReport'
import {
  Document,
  Download,
  ArrowLeft,
  Loading,
  User,
  Trophy,
  Check,
  Warning,
  Right,
  DataAnalysis
} from '@element-plus/icons-vue'

const router = useRouter()
const trainingReportStore = useTrainingReportStore()
const patientStore = usePatientStore()
const trainingStore = useTrainingStore()
const workflowStore = useWorkflowStore()
const connectionStore = useConnectionStore()
const simpleIdentity = useSimpleIdentityVerification()

// 响应式数据
const loading = ref(true)
const expandedActions = ref([])
const activeCollapse = ref(['achievements', 'focus', 'goals'])

// 自动清理相关
const patientIdCheckTimer = ref(null)
const reportDisplayTimer = ref(null)
const patientIdMissingCount = ref(0)
const reportStartTime = ref(null)

// 计算属性
const reportData = computed(() => trainingReportStore.currentSessionReport)

// 基本信息项目
const basicInfoItems = computed(() => {
  if (!reportData.value) return []
  return [
    { label: '训练人员', value: reportData.value.patient_info.patient_name, type: 'success' },
    { label: '训练日期', value: reportData.value.patient_info.session_date, type: 'info' },
    { label: '开始时间', value: formatTime(reportData.value.session_info.start_time), type: 'info' },
    { label: '结束时间', value: formatTime(reportData.value.session_info.end_time), type: 'info' },
    { label: '训练时长', value: formatDuration(reportData.value.session_info.total_duration), type: 'warning' },
    { label: '完成动作', value: `${reportData.value.session_info.completed_actions}/${reportData.value.session_info.total_actions}`, type: 'primary' }
  ]
})

// 统计数据项目
const statisticsItems = computed(() => {
  if (!reportData.value) return []
  return [
    { label: '平均得分', value: reportData.value.statistics.average_score, color: 'text-blue-500' },
    { label: '完成率', value: `${reportData.value.statistics.completion_rate}%`, color: 'text-green-500' },
    { label: '完成阶段数', value: reportData.value.statistics.total_stages_completed, color: 'text-orange-500' },
    // { label: '反馈记录数', value: reportData.value.metadata.total_feedback_records, color: 'text-purple-500' }
  ]
})

// 自动清理函数
const clearAllDataAndReturnToLogin = () => {
  console.log('[ReportView] 执行自动清理，返回登录页')

  // 清理定时器
  if (patientIdCheckTimer.value) {
    clearInterval(patientIdCheckTimer.value)
    patientIdCheckTimer.value = null
  }
  if (reportDisplayTimer.value) {
    clearTimeout(reportDisplayTimer.value)
    reportDisplayTimer.value = null
  }

  // 清理所有数据
  patientStore.resetPatientData()
  trainingStore.resetTrainingData()
  trainingReportStore.resetReportData()

  // 重置工作流状态为waiting
  workflowStore.transitionTo('waiting')

  // // 跳转到登录页
  // router.push('/')
}

// 检查患者ID的函数
const checkPatientId = () => {
  const currentPatientId = connectionStore.patientId || patientStore.userInfo?.patient_id

  if (!currentPatientId) {
    patientIdMissingCount.value++
    console.log(`[ReportView] 患者ID缺失计数: ${patientIdMissingCount.value}/5`)

    if (patientIdMissingCount.value >= 5) { // 5次检查 = 10秒
      console.log('[ReportView] 患者ID持续缺失10秒，触发自动清理')
      clearAllDataAndReturnToLogin()
    }
  } else {
    // 重置计数
    patientIdMissingCount.value = 0
  }
}

// 启动监控
const startMonitoring = () => {
  console.log('[ReportView] 启动报告页面身份验证监控')
  reportStartTime.value = Date.now()
  // 启动身份验证系统（报告阶段30秒超时）
  simpleIdentity.startVerification({
    onUserMissing: (data) => {
      console.log('[ReportView] 用户消失，30秒后将自动清理')
    },
  })
  // 1分钟后自动清理（备用机制）
  reportDisplayTimer.value = setTimeout(() => {
    console.log('[ReportView] 报告展示超过1分钟，触发自动清理')
    clearAllDataAndReturnToLogin()
  }, 60000) // 60秒
}

// 停止监控
const stopMonitoring = () => {
  console.log('[ReportView] 停止报告页面监控')
  // 停止身份验证
  simpleIdentity.stopVerification()
  if (patientIdCheckTimer.value) {
    clearInterval(patientIdCheckTimer.value)
    patientIdCheckTimer.value = null
  }
  if (reportDisplayTimer.value) {
    clearTimeout(reportDisplayTimer.value)
    reportDisplayTimer.value = null
  }
}

// 生命周期
onMounted(async () => {
  try {
    // 如果没有当前报告数据，尝试从本地存储加载最新的报告
    if (!reportData.value) {
      console.log('[ReportView] 没有当前报告数据，尝试加载最新报告')
      // 这里可以添加从本地存储或API加载报告的逻辑
    }
    // 启动自动清理监控
    startMonitoring()
    loading.value = false
  } catch (error) {
    console.error('[ReportView] 加载报告数据失败:', error)
    loading.value = false
  }
})

onUnmounted(() => {
  // 组件卸载时停止监控
  stopMonitoring()
})

// 方法
const goBack = () => {
  // 停止监控
  stopMonitoring()
  router.push('/training')
}

const exportReport = () => {
  if (!reportData.value) return

  try {
    // 创建导出数据
    const exportData = {
      ...reportData.value,
      export_time: new Date().toISOString(),
      export_version: '1.0.0'
    }

    // 创建下载链接
    const dataStr = JSON.stringify(exportData, null, 2)
    const dataBlob = new Blob([dataStr], { type: 'application/json' })
    const url = URL.createObjectURL(dataBlob)

    // 创建下载元素
    const link = document.createElement('a')
    link.href = url
    link.download = `训练报告_${reportData.value.patient_info.patient_name}_${new Date().toLocaleDateString('zh-CN')}.json`
    document.body.appendChild(link)
    link.click()
    document.body.removeChild(link)

    // 清理URL对象
    URL.revokeObjectURL(url)

    console.log('[ReportView] 报告导出成功')
  } catch (error) {
    console.error('[ReportView] 报告导出失败:', error)
  }
}

const toggleActionExpand = (index) => {
  const expandedIndex = expandedActions.value.indexOf(index)
  if (expandedIndex > -1) {
    expandedActions.value.splice(expandedIndex, 1)
  } else {
    expandedActions.value.push(index)
  }
}

const formatTime = (isoString) => {
  if (!isoString) return '--'
  return new Date(isoString).toLocaleString('zh-CN')
}

const formatDuration = (seconds) => {
  if (!seconds || seconds <= 0) return '--'

  // 输入确定是秒数
  const totalSeconds = Math.floor(seconds)
  const minutes = Math.floor(totalSeconds / 60)
  const remainingSeconds = totalSeconds % 60

  if (minutes > 0) {
    return `${minutes}分${remainingSeconds}秒`
  } else {
    return `${remainingSeconds}秒`
  }
}

const getScoreClass = (score) => {
  if (score >= 90) return 'bg-green-100 text-green-800'
  if (score >= 75) return 'bg-blue-100 text-blue-800'
  if (score >= 60) return 'bg-orange-100 text-orange-800'
  return 'bg-red-100 text-red-800'
}

// Element Plus 相关方法
const getProgressColor = (score) => {
  if (score >= 90) return '#67c23a'
  if (score >= 75) return '#409eff'
  if (score >= 60) return '#e6a23c'
  return '#f56c6c'
}

const getLevelTagType = (level) => {
  const types = {
    excellent: 'success',
    good: 'primary',
    fair: 'warning',
    needs_improvement: 'danger'
  }
  return types[level] || 'info'
}



const getLevelLabel = (level) => {
  return OVERALL_ASSESSMENT_LEVELS[level]?.label || level
}

const getDifficultyLabel = (difficulty) => {
  const labels = {
    easy: '简单',
    medium: '中等',
    hard: '困难'
  }
  return labels[difficulty] || difficulty
}

const getStageLabel = (stage) => {
  const labels = {
    raising: '抬起阶段',
    holding: '保持阶段',
    lowering: '放下阶段',
    moving: '移动阶段',
    returning: '返回阶段',
    flipping: '翻转阶段',
    finger_食指: '食指对指',
    finger_中指: '中指对指',
    finger_无名指: '无名指对指',
    finger_小指: '小指对指'
  }
  return labels[stage] || stage
}
</script>