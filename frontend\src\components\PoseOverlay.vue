<!-- components/PoseOverlay.vue -->
<template>
  <canvas ref="poseCanvas" class="pose-overlay"></canvas>
</template>

<script setup>
import { ref, watch, onUnmounted, nextTick } from 'vue';
import { storeToRefs } from 'pinia';
import { useConnectionStore } from '@/stores/connection';
import { KeypointRenderer } from '@/utils/keypointRenderer';
import { COCO_WHOLEBODY_CONNECTIONS } from '@/utils/poseConstants';

const props = defineProps({
  targetRef: {
    type: Object, // 接收持有VideoStream组件实例的ref
    default: null,
  },
  poseRenderOptions: {
    type: Object,
    default: () => ({
      pointRadius: 4,
      lineWidth: 2,
      pointColor: 'red',
      lineColor: 'lime',
      confidenceThreshold: 0.2,
      smoothingFactor: 0.4, // 平滑因子
      fadeOutFactor: 0.15, // 置信度衰减因子
      maxMissingFrames: 10, // 最大缺失帧数
    }),
  },
});

const connectionStore = useConnectionStore();
const { poseKeypoints } = storeToRefs(connectionStore);

const poseCanvas = ref(null);
const poseRenderer = ref(null);
let resizeObserver = null;

const setupCanvas = (containerEl, imageEl) => {
  if (!poseCanvas.value || !containerEl || !imageEl) return;

  const rect = containerEl.getBoundingClientRect();
  if (rect.width === 0 || rect.height === 0) return;

  const canvas = poseCanvas.value;
  const dpr = window.devicePixelRatio || 1;
  
  // 设置Canvas的实际尺寸（考虑设备像素比）
  canvas.width = rect.width * dpr;
  canvas.height = rect.height * dpr;
  canvas.style.width = `${rect.width}px`;
  canvas.style.height = `${rect.height}px`;

  const ctx = canvas.getContext('2d');
  // 重要：缩放上下文以匹配设备像素比
  ctx.scale(dpr, dpr);
  
  // 设置Canvas渲染质量
  ctx.imageSmoothingEnabled = true;
  ctx.imageSmoothingQuality = 'high';

  if (!poseRenderer.value) {
    poseRenderer.value = new KeypointRenderer(canvas, {
      ...props.poseRenderOptions,
      connections: COCO_WHOLEBODY_CONNECTIONS,
    });
    poseRenderer.value.startRenderLoop();
  } else {
    // 停止旧的渲染循环，重新设置
    poseRenderer.value.stopRenderLoop();
    poseRenderer.value.updateCanvas(canvas);
    poseRenderer.value.startRenderLoop();
  }
  // 关键：每次更新时都传递 image 元素给渲染器
  poseRenderer.value.updateImageElement(imageEl);
};

// 监听转换后的坐标数据并传递给store
const updateTransformedKeypoints = () => {
  if (poseRenderer.value) {
    const transformedKeypoints = poseRenderer.value.getTransformedKeypoints();
    const transformParams = poseRenderer.value.getTransformParams();
    // 将转换后的坐标传递给store
    connectionStore.updateTransformedKeypoints(transformedKeypoints, transformParams);
  }
};

watch(
  () => props.targetRef,
  async (componentInstance) => {
    const containerEl = componentInstance?.containerRef;
    const imageEl = componentInstance?.imageRef;

    if (containerEl && imageEl) {
      await nextTick();
      if (poseCanvas.value) {
        setupCanvas(containerEl, imageEl);

        if (resizeObserver) resizeObserver.disconnect();
        
        resizeObserver = new ResizeObserver(() => {
          setupCanvas(containerEl, imageEl);
          // 画布尺寸变化后更新转换坐标
          updateTransformedKeypoints();
        });
        resizeObserver.observe(containerEl);
      }
    }
  },
  { deep: true, flush: 'post' }
);

watch(poseKeypoints, (newKeypoints) => {
  if (poseRenderer.value) {
    poseRenderer.value.updateKeypoints(newKeypoints || []);
    // 关键点更新后更新转换坐标
    updateTransformedKeypoints();
  }
}, { deep: true });

onUnmounted(() => {
  if (resizeObserver) {
    resizeObserver.disconnect();
  }
  if (poseRenderer.value) {
    poseRenderer.value.destroy();
  }
});
</script>

<style scoped>
.pose-overlay {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  pointer-events: none;
  z-index: 10;
}
</style>
